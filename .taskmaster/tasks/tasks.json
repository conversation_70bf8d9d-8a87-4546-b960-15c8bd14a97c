{"master": {"tasks": [{"id": 1, "title": "Project Setup & Core Infrastructure", "description": "Initialize the Node.js/TypeScript project, configure environment variables, set up core Express server with rate limiting, implement global error handling, configure CORS, integrate basic logging and security middleware, and establish database connection using Prisma.", "status": "done", "dependencies": [], "priority": "high", "details": "Initialize a new Node.js project with `npm init -y` and `tsc --init`. Install Express, TypeScript, dotenv, morgan, helmet, cors, `express-rate-limit`, `prisma`, and `@prisma/client`. Configure `tsconfig.json` for strict mode. Create an `src/app.ts` for the Express app. Set up `.env` for `PORT=3000`, `CORS_ORIGIN=http://localhost:10000`, and `DATABASE_URL` (e.g., for PostgreSQL). Implement a global error handling middleware (e.g., `(err, req, res, next) => { res.status(err.statusCode || 500).json({ message: err.message || 'Server Error' }); }`). Configure `cors({ origin: process.env.CORS_ORIGIN, credentials: true })`. Use `app.use(helmet())` for security headers and `app.use(morgan('dev'))` for request logging. Implement `express-rate-limit` middleware, ensuring `AuthenticatedRequest` type is correctly used if applicable. Create a basic `/health` endpoint. Configure Prisma client and ensure it can connect to the specified `DATABASE_URL` (local or Neon PostgreSQL).", "testStrategy": "Verify server starts on port 3000. Test `/health` endpoint returns 200 OK. Use Postman/curl to send requests from `http://localhost:10000` to ensure CORS is correctly configured. Verify error handling middleware catches and formats errors for non-existent routes or thrown exceptions. Check console logs for Morgan output and ensure Helmet adds security headers. Verify rate limiting is active and correctly blocks excessive requests. Verify Prisma client connects successfully to the configured database. Test a simple database query (e.g., a health check endpoint that queries the DB) to confirm connectivity.", "subtasks": [{"id": "1.1", "description": "TypeScript configuration completed - fixed duplicate noUncheckedIndexedAccess in tsconfig.json.", "status": "done"}, {"id": "1.2", "description": "Express server setup completed - all middleware configured (helmet, CORS, morgan, rate limiting, error handling).", "status": "done"}, {"id": "1.3", "description": "Server startup successful - server starts on port 3000 with proper logging.", "status": "done"}, {"id": "1.4", "description": "Rate limiter TypeScript errors fixed - updated to use AuthenticatedRequest type.", "status": "done"}, {"id": "1.5", "description": "Configure proper database connection using Prisma client (Neon PostgreSQL or local setup) and resolve connection issues.", "status": "done"}]}, {"id": 2, "title": "Database Integration with Prisma", "description": "Neon PostgreSQL database has been successfully integrated using Prisma ORM, with the schema adapted, connection pooling configured, migration strategy implemented, and seeding scripts created. The database is now fully operational and ready for API endpoint development.", "status": "done", "dependencies": [1], "priority": "high", "details": "The initial setup including Prisma CLI and client installation, schema configuration, environment variable setup for Neon PostgreSQL with pgbouncer, Prisma client generation, database connection utility, initial migration, and seeding scripts for User, Patient, and Provider data has been completed. The database is ready for consumption by API endpoints.", "testStrategy": "Initial verification of schema application, data seeding, and basic Prisma query functionality has been completed. Ongoing monitoring of Neon dashboard for connection pooling metrics is recommended. Further integration tests will be covered in API endpoint development tasks.", "subtasks": [{"id": "2.1", "description": "Neon PostgreSQL database successfully integrated with Prisma ORM.", "status": "done"}, {"id": "2.2", "description": "Database schema pushed to Neon (16 users, 8 patients, 6 providers, 8 appointments).", "status": "done"}, {"id": "2.3", "description": "Prisma client generated and working.", "status": "done"}, {"id": "2.4", "description": "Database connection established and tested.", "status": "done"}, {"id": "2.5", "description": "Seed script created with sample admin, provider, and patient accounts.", "status": "done"}, {"id": "2.6", "description": "Connection pooling configured with pgbouncer.", "status": "done"}]}, {"id": 3, "title": "Core Authentication Endpoints (Register, Login)", "description": "Implement core user authentication endpoints including registration and login, utilizing JWT for token generation and bcryptjs for secure password hashing.", "details": "Install `jsonwebtoken` and `bcryptjs`. Create `src/controllers/authController.ts` and `src/routes/authRoutes.ts`. Implement `POST /api/auth/register` endpoint: Validate input using <PERSON><PERSON> (email, password, role). Hash password with `bcryptjs.hash(password, 12)`. Create user in DB via Prisma. Generate JWT (`jwt.sign({ id: user.id, role: user.role }, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN })`). Implement `POST /api/auth/login`: Validate input. Compare password with `bcryptjs.compare(password, user.password)`. Generate JWT on successful login. Set JWT as an HttpOnly cookie. Implement rate limiting on login endpoint using `express-rate-limit`.", "testStrategy": "Use Jest and Supertest to write integration tests for `POST /api/auth/register` (success, invalid input, duplicate email) and `POST /api/auth/login` (success, wrong credentials, rate limiting). Verify JWT is generated and set as HttpOnly cookie. Check password hashing by attempting to log in with the registered password.", "priority": "high", "dependencies": [1, 2], "status": "done", "subtasks": []}, {"id": 4, "title": "Authentication - Password Reset & Refresh Tokens", "description": "Develop the password reset flow, including secure token generation, email delivery for verification, and updating the password, along with a refresh token mechanism for extended sessions.", "details": "For password reset: Implement `POST /api/auth/forgot-password` to generate a unique, time-limited token, store it in DB (e.g., on User model), and send it via email (SMTP integration, e.g., Nodemailer). Implement `POST /api/auth/reset-password/:token` to validate the token, hash the new password, and update the user's password. For refresh tokens: Implement `POST /api/auth/refresh-token` to issue a new access token using a stored refresh token. Store refresh tokens securely (e.g., in a separate DB table or encrypted in HttpOnly cookies). Implement token invalidation on logout.", "testStrategy": "Test password reset flow end-to-end: Request reset, verify email receipt (mock SMTP or test email service), use token to reset password, then log in with new password. Test refresh token endpoint: Obtain refresh token, use it to get a new access token, verify old access token is invalid. Ensure tokens expire correctly.", "priority": "high", "dependencies": [3], "status": "pending", "subtasks": []}, {"id": 5, "title": "Authentication - Role-Based Access Control & Logout", "description": "Implement role-based authorization middleware to protect API routes based on user roles (PATIENT, PROVIDER, ADMIN) and develop session management with logout functionality.", "details": "Create an `authMiddleware.ts` to verify JWTs and extract user roles. Implement a `restrictTo` middleware function that takes an array of roles and checks if the authenticated user's role is included. Apply this middleware to protected routes (e.g., `router.get('/admin/users', protect, restrictTo('ADMIN'), adminController.getUsers)`). Implement `POST /api/auth/logout` to clear JWT and refresh token cookies, effectively ending the session. Ensure proper error handling for unauthorized access.", "testStrategy": "Write integration tests for protected routes: Verify access for correct roles, deny access for incorrect roles (403 Forbidden), and deny access for unauthenticated users (401 Unauthorized). Test logout functionality by attempting to access protected routes after logging out.", "priority": "high", "dependencies": [3, 4], "status": "done", "subtasks": []}, {"id": 6, "title": "User Profile Management (CRUD)", "description": "Implement comprehensive CRUD operations for user profiles, including patient and provider specific fields, and integrate user avatar upload with a file storage service.", "details": "Implement `GET /api/users/profile` to retrieve authenticated user's profile. Implement `PUT /api/users/profile` for updating profile details (e.g., name, contact info, medical history for patients, specialty for providers). Use Zod for robust input validation. Implement `DELETE /api/users/account` for account deactivation/deletion (soft delete recommended for HIPAA). Integrate with AWS S3 or Cloudinary for avatar uploads: Use Multer for file upload middleware, then upload to chosen service and store URL in DB. Ensure role-specific fields are handled correctly.", "testStrategy": "Test profile retrieval for different roles. Test profile updates with valid and invalid data, ensuring only allowed fields are updated. Test avatar upload functionality, verifying file is stored and URL is updated in DB. Test account deactivation/deletion and verify user can no longer log in or access data.", "priority": "high", "dependencies": [5], "status": "in-progress", "subtasks": []}, {"id": 7, "title": "Patient Onboarding Data Integration", "description": "Process and store multi-step patient onboarding data from the frontend, including structured medical history, insurance information, and emergency contact details.", "details": "Create a dedicated endpoint, e.g., `POST /api/patients/onboarding`, to receive the multi-step data. Define a Zod schema for the comprehensive onboarding data (allergies, conditions, medications, insurance details, emergency contacts, consent). Use Prisma to store this data, potentially across multiple related models (e.g., `MedicalRecord`, `Insurance`, `EmergencyContact` linked to `Patient`). Ensure data validation is strict and handles sensitive health information securely. Implement patient data export functionality (e.g., `GET /api/patients/profile/export`) for compliance.", "testStrategy": "Simulate frontend onboarding flow via integration tests, sending multi-step data to the backend. Verify all data points are correctly stored in the database. Test edge cases like missing required fields or invalid data formats. Test patient data export functionality, ensuring the generated data is accurate and complete.", "priority": "medium", "dependencies": [6], "status": "pending", "subtasks": []}, {"id": 8, "title": "Provider Management Workflow", "description": "Implement the provider registration and approval workflow, including admin-controlled approval/rejection, medical license verification, and management of provider availability schedules.", "details": "Implement `GET /api/admin/providers/pending` for admin to view unapproved providers. Implement `PUT /api/admin/providers/:id/approve` and `PUT /api/admin/providers/:id/reject` endpoints, accessible only by ADMIN role. Update provider status in DB. Develop a system to store and track medical license details (e.g., license number, expiry, state). Implement `PUT /api/providers/:id/availability` for providers to manage their schedule (e.g., array of available time slots, days). Use Zod for availability input validation. Ensure providers can only update their own availability.", "testStrategy": "Test provider registration (as a user) and verify it's initially pending. Test admin approval/rejection endpoints, ensuring only admins can perform these actions. Verify provider status changes correctly. Test provider availability updates, ensuring time slots are stored and validated. Simulate concurrent updates to availability to check for race conditions.", "priority": "medium", "dependencies": [6], "status": "pending", "subtasks": []}, {"id": 9, "title": "Advanced Scheduling Engine Core Logic", "description": "Develop the core logic for the advanced scheduling engine, including real-time availability checking, conflict resolution, multi-timezone support, and functionality for recurring appointments.", "details": "Design a robust data model for `Availability` and `Appointment` in Prisma, considering time zones. Implement a service layer function `getAvailableSlots(providerId, date, timezone)` that queries provider availability and existing appointments to return truly available slots. Implement conflict resolution logic to prevent double bookings. For multi-timezone support, store all times in UTC and convert to/from user's specified timezone for display/input. For recurring appointments, implement logic to create multiple `Appointment` records based on a recurrence pattern (e.g., daily, weekly, monthly) and a defined end date/count.", "testStrategy": "Write unit tests for `getAvailableSlots` function with various scenarios: full availability, partial availability, existing appointments, different time zones. Test conflict resolution by attempting to book overlapping appointments. Test recurring appointment creation, verifying correct number of appointments are created with accurate dates and times. Verify timezone conversions are accurate.", "priority": "high", "dependencies": [2, 6, 8], "status": "pending", "subtasks": []}, {"id": 10, "title": "Appointment Lifecycle Management", "description": "Implement the full appointment lifecycle management, covering creation, status transitions (e.g., SCHEDULED, CONFIRMED, COMPLETED, CANCELLED), modification, rescheduling, and cancellation policies.", "details": "Implement `POST /api/appointments` endpoint: Validate input (patient, provider, time, type), use the scheduling engine to confirm availability, create the appointment record in DB. Implement `PUT /api/appointments/:id/status` to allow status transitions (e.g., `SCHEDULED` to `CONFIRMED`, `COMPLETED`, `CANCELLED`). Implement `PUT /api/appointments/:id/reschedule` to allow changing appointment time/date, re-checking availability. Implement cancellation policies (e.g., `POST /api/appointments/:id/cancel`) including potential fee management (future integration with payments). Ensure only authorized users (patient for their own, provider for their own, admin for all) can modify appointments.", "testStrategy": "Test appointment creation for valid and invalid scenarios (e.g., unavailable slot). Test all status transitions, ensuring only valid transitions are allowed. Test rescheduling, verifying old slot is freed and new slot is booked. Test cancellation, ensuring status updates and any associated logic (e.g., fee calculation) is triggered. Verify authorization for all appointment actions.", "priority": "high", "dependencies": [9], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-06-18T02:14:37.887Z", "updated": "2025-06-20T18:29:19.084Z", "description": "Tasks for master context"}}}