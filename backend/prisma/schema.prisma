// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          String    @default("PATIENT")
  phone         String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts      Account[]
  sessions      Session[]
  patient       Patient?
  provider      Provider?
  notifications Notification[]
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Patient {
  id             String    @id @default(cuid())
  userId         String    @unique
  dateOfBirth    DateTime?
  address        String?
  phone          String?
  emergencyContact Json?
  medicalHistory Json?
  insurance      Json?
  preferences    Json?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  user           User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  appointments   Appointment[]
  medicalRecords MedicalRecord[]
  payments       Payment[]
}

model Provider {
  id             String    @id @default(cuid())
  userId         String    @unique
  specialization String?
  bio            String?   @db.Text
  education      Json?
  experience     Json?
  licenseNumber  String?
  isVerified     Boolean   @default(false)
  isActive       Boolean   @default(true)
  consultationFee Decimal? @db.Decimal(10, 2)
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  user           User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  appointments   Appointment[]
  availability   Availability[]
  medicalRecords MedicalRecord[]
}

model Appointment {
  id              String   @id @default(cuid())
  providerId      String
  patientId       String
  appointmentDate DateTime
  reason          String?  @db.Text
  status          String   @default("SCHEDULED")
  consultationType String   @default("VIDEO") // VIDEO or AUDIO
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  provider     Provider      @relation(fields: [providerId], references: [id], onDelete: Cascade)
  patient      Patient       @relation(fields: [patientId], references: [id], onDelete: Cascade)
  consultation Consultation?
  payment      Payment?
}

model Consultation {
  id            String   @id @default(cuid())
  appointmentId String   @unique
  roomUrl       String
  status        String   @default("SCHEDULED")
  videoEnabled  Boolean  @default(false) // Tracks if video was enabled in an audio call
  notes         String?  @db.Text
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  appointment   Appointment    @relation(fields: [appointmentId], references: [id], onDelete: Cascade)
  medicalRecord MedicalRecord?
}

model Notification {
  id        String   @id @default(cuid())
  title     String
  message   String   @db.Text
  type      String
  userId    String
  relatedId String?
  isRead    Boolean  @default(false)
  link      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Availability {
  id          String   @id @default(cuid())
  providerId  String
  dayOfWeek   String   // MONDAY, TUESDAY, etc.
  startTime   String   // HH:MM format
  endTime     String   // HH:MM format
  isAvailable Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  provider Provider @relation(fields: [providerId], references: [id], onDelete: Cascade)

  @@unique([providerId, dayOfWeek, startTime])
}

model MedicalRecord {
  id             String   @id @default(cuid())
  patientId      String
  providerId     String
  consultationId String?  @unique
  diagnosis      String?  @db.Text
  notes          String?  @db.Text
  prescriptions  Json?
  attachments    Json?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  patient      Patient       @relation(fields: [patientId], references: [id], onDelete: Cascade)
  provider     Provider      @relation(fields: [providerId], references: [id], onDelete: Cascade)
  consultation Consultation? @relation(fields: [consultationId], references: [id], onDelete: SetNull)
}

model Payment {
  id               String    @id @default(cuid())
  patientId        String
  appointmentId    String?   @unique
  amount           Decimal   @db.Decimal(10, 2)
  currency         String    @default("USD")
  status           String    @default("PENDING") // PENDING, COMPLETED, FAILED, REFUNDED
  paymentMethod    String    // STRIPE, PAYSTACK, PAYPAL, FLUTTERWAVE
  paymentIntentId  String?
  transactionId    String?
  metadata         Json?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt

  patient     Patient      @relation(fields: [patientId], references: [id], onDelete: Cascade)
  appointment Appointment? @relation(fields: [appointmentId], references: [id], onDelete: SetNull)
}
