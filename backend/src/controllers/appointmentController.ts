import { Response } from 'express';
import { prisma } from '@/config/database';
import logger from '@/config/logger';
import { AuthenticatedRequest } from '@/types';

/**
 * Create new appointment
 * POST /api/appointments
 */
export const createAppointment = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'Authentication required',
      });
      return;
    }

    if (req.user.role !== 'PATIENT') {
      res.status(403).json({
        success: false,
        error: 'Only patients can book appointments',
      });
      return;
    }

    const { providerId, appointmentDate, reason, consultationType = 'VIDEO' } = req.body;

    // Get patient ID
    const patient = await prisma.patient.findUnique({
      where: { userId: req.user.id },
      select: { id: true },
    });

    if (!patient) {
      res.status(404).json({
        success: false,
        error: 'Patient profile not found',
      });
      return;
    }

    // Verify provider exists and is active
    const provider = await prisma.provider.findUnique({
      where: { id: providerId },
      select: { 
        id: true, 
        isVerified: true, 
        isActive: true,
        user: {
          select: { name: true }
        }
      },
    });

    if (!provider || !provider.isVerified || !provider.isActive) {
      res.status(400).json({
        success: false,
        error: 'Provider not available',
      });
      return;
    }

    // Check for conflicting appointments
    const existingAppointment = await prisma.appointment.findFirst({
      where: {
        providerId,
        appointmentDate: new Date(appointmentDate),
        status: {
          in: ['SCHEDULED', 'IN_PROGRESS'],
        },
      },
    });

    if (existingAppointment) {
      res.status(400).json({
        success: false,
        error: 'Provider is not available at this time',
      });
      return;
    }

    // Create appointment
    const appointment = await prisma.appointment.create({
      data: {
        providerId,
        patientId: patient.id,
        appointmentDate: new Date(appointmentDate),
        reason,
        consultationType,
        status: 'SCHEDULED',
      },
      include: {
        provider: {
          select: {
            id: true,
            specialization: true,
            consultationFee: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
        patient: {
          select: {
            id: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
      },
    });

    res.status(201).json({
      success: true,
      data: { appointment },
      message: 'Appointment booked successfully',
    });
  } catch (error) {
    logger.error('Create appointment error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create appointment',
    });
  }
};

/**
 * Get user's appointments
 * GET /api/appointments
 */
export const getAppointments = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'Authentication required',
      });
      return;
    }

    const { status, page = '1', limit = '10' } = req.query;
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    let where: any = {};

    if (req.user.role === 'PATIENT') {
      const patient = await prisma.patient.findUnique({
        where: { userId: req.user.id },
        select: { id: true },
      });

      if (!patient) {
        res.status(404).json({
          success: false,
          error: 'Patient profile not found',
        });
        return;
      }

      where.patientId = patient.id;
    } else if (req.user.role === 'PROVIDER') {
      const provider = await prisma.provider.findUnique({
        where: { userId: req.user.id },
        select: { id: true },
      });

      if (!provider) {
        res.status(404).json({
          success: false,
          error: 'Provider profile not found',
        });
        return;
      }

      where.providerId = provider.id;
    }

    if (status) {
      where.status = status;
    }

    const [appointments, total] = await Promise.all([
      prisma.appointment.findMany({
        where,
        include: {
          provider: {
            select: {
              id: true,
              specialization: true,
              consultationFee: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
          patient: {
            select: {
              id: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
          consultation: {
            select: {
              id: true,
              roomUrl: true,
              status: true,
            },
          },
        },
        skip,
        take: limitNum,
        orderBy: {
          appointmentDate: 'desc',
        },
      }),
      prisma.appointment.count({ where }),
    ]);

    res.json({
      success: true,
      data: {
        appointments,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum),
        },
      },
    });
  } catch (error) {
    logger.error('Get appointments error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get appointments',
    });
  }
};

/**
 * Get specific appointment
 * GET /api/appointments/:id
 */
export const getAppointment = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'Authentication required',
      });
      return;
    }

    const { id } = req.params;

    const appointment = await prisma.appointment.findUnique({
      where: { id },
      include: {
        provider: {
          select: {
            id: true,
            specialization: true,
            bio: true,
            consultationFee: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
        patient: {
          select: {
            id: true,
            dateOfBirth: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true,
              },
            },
          },
        },
        consultation: {
          select: {
            id: true,
            roomUrl: true,
            status: true,
            notes: true,
          },
        },
        payment: {
          select: {
            id: true,
            amount: true,
            status: true,
            paymentMethod: true,
          },
        },
      },
    });

    if (!appointment) {
      res.status(404).json({
        success: false,
        error: 'Appointment not found',
      });
      return;
    }

    // Check authorization
    const isPatient = req.user.role === 'PATIENT' && appointment.patient.user.id === req.user.id;
    const isProvider = req.user.role === 'PROVIDER' && appointment.provider.user.id === req.user.id;
    const isAdmin = req.user.role === 'ADMIN';

    if (!isPatient && !isProvider && !isAdmin) {
      res.status(403).json({
        success: false,
        error: 'Access denied',
      });
      return;
    }

    res.json({
      success: true,
      data: { appointment },
    });
  } catch (error) {
    logger.error('Get appointment error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get appointment',
    });
  }
};

/**
 * Update appointment status
 * PUT /api/appointments/:id/status
 */
export const updateAppointmentStatus = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'Authentication required',
      });
      return;
    }

    const { id } = req.params;
    const { status } = req.body;

    const validStatuses = ['SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'NO_SHOW'];
    if (!validStatuses.includes(status)) {
      res.status(400).json({
        success: false,
        error: 'Invalid status',
      });
      return;
    }

    const appointment = await prisma.appointment.findUnique({
      where: { id },
      include: {
        provider: {
          select: {
            user: { select: { id: true } }
          }
        },
        patient: {
          select: {
            user: { select: { id: true } }
          }
        }
      },
    });

    if (!appointment) {
      res.status(404).json({
        success: false,
        error: 'Appointment not found',
      });
      return;
    }

    // Check authorization
    const isPatient = req.user.role === 'PATIENT' && appointment.patient.user.id === req.user.id;
    const isProvider = req.user.role === 'PROVIDER' && appointment.provider.user.id === req.user.id;
    const isAdmin = req.user.role === 'ADMIN';

    if (!isPatient && !isProvider && !isAdmin) {
      res.status(403).json({
        success: false,
        error: 'Access denied',
      });
      return;
    }

    // Business rules for status changes
    if (status === 'CANCELLED' && appointment.status === 'COMPLETED') {
      res.status(400).json({
        success: false,
        error: 'Cannot cancel completed appointment',
      });
      return;
    }

    const updatedAppointment = await prisma.appointment.update({
      where: { id },
      data: { status },
      include: {
        provider: {
          select: {
            id: true,
            specialization: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
        patient: {
          select: {
            id: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
      },
    });

    res.json({
      success: true,
      data: { appointment: updatedAppointment },
      message: 'Appointment status updated successfully',
    });
  } catch (error) {
    logger.error('Update appointment status error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update appointment status',
    });
  }
};

/**
 * Cancel appointment
 * DELETE /api/appointments/:id
 */
export const cancelAppointment = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'Authentication required',
      });
      return;
    }

    const { id } = req.params;

    const appointment = await prisma.appointment.findUnique({
      where: { id },
      include: {
        provider: {
          select: {
            user: { select: { id: true } }
          }
        },
        patient: {
          select: {
            user: { select: { id: true } }
          }
        }
      },
    });

    if (!appointment) {
      res.status(404).json({
        success: false,
        error: 'Appointment not found',
      });
      return;
    }

    // Check authorization
    const isPatient = req.user.role === 'PATIENT' && appointment.patient.user.id === req.user.id;
    const isProvider = req.user.role === 'PROVIDER' && appointment.provider.user.id === req.user.id;
    const isAdmin = req.user.role === 'ADMIN';

    if (!isPatient && !isProvider && !isAdmin) {
      res.status(403).json({
        success: false,
        error: 'Access denied',
      });
      return;
    }

    // Check if appointment can be cancelled
    if (appointment.status === 'COMPLETED') {
      res.status(400).json({
        success: false,
        error: 'Cannot cancel completed appointment',
      });
      return;
    }

    if (appointment.status === 'CANCELLED') {
      res.status(400).json({
        success: false,
        error: 'Appointment is already cancelled',
      });
      return;
    }

    const updatedAppointment = await prisma.appointment.update({
      where: { id },
      data: { status: 'CANCELLED' },
    });

    res.json({
      success: true,
      data: { appointment: updatedAppointment },
      message: 'Appointment cancelled successfully',
    });
  } catch (error) {
    logger.error('Cancel appointment error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to cancel appointment',
    });
  }
};
