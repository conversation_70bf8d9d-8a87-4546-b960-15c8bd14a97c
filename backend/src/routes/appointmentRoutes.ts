import { Router } from 'express';
import { 
  createAppointment,
  getAppointments,
  getAppointment,
  updateAppointmentStatus,
  cancelAppointment
} from '@/controllers/appointmentController';
import { authenticateToken, authorizeRoles } from '@/middleware/auth';
import { validate } from '@/middleware/validation';

const router = Router();

/**
 * @route   POST /api/appointments
 * @desc    Create new appointment (patients only)
 * @access  Private (Patient)
 */
router.post(
  '/',
  authenticateToken,
  authorizeRoles('PATIENT'),
  createAppointment
);

/**
 * @route   GET /api/appointments
 * @desc    Get user's appointments (patients see their appointments, providers see their appointments)
 * @access  Private
 */
router.get(
  '/',
  authenticateToken,
  getAppointments
);

/**
 * @route   GET /api/appointments/:id
 * @desc    Get specific appointment details
 * @access  Private (Patient, Provider, Admin)
 */
router.get(
  '/:id',
  authenticateToken,
  getAppointment
);

/**
 * @route   PUT /api/appointments/:id/status
 * @desc    Update appointment status
 * @access  Private (Patient, Provider, Admin)
 */
router.put(
  '/:id/status',
  authenticateToken,
  updateAppointmentStatus
);

/**
 * @route   DELETE /api/appointments/:id
 * @desc    Cancel appointment
 * @access  Private (Patient, Provider, Admin)
 */
router.delete(
  '/:id',
  authenticateToken,
  cancelAppointment
);

export default router;
